import requests
from lxml import etree

def decode_secret_message(url: str) -> None:
    """
    Stream-parse the published Google Doc and print the secret grid.
    """
    try:
        resp = requests.get(url, stream=True)
        resp.raise_for_status()
        # ensure .raw is decompressed
        resp.raw.decode_content = True
    except requests.RequestException as e:
        print(f"Error fetching document: {e}")
        return

    _parse_and_print_stream(resp.raw)

def _parse_and_print_stream(stream) -> None:
    # iterparse over <tr> tags
    context = etree.iterparse(stream, tag='tr', html=True)

    first = True
    points = []           # will hold (x,y,char)
    max_x = max_y = 0

    for _, tr in context:
        if first:
            first = False   # skip header row
        else:
            tds = tr.findall('.//td')
            if len(tds) >= 3:
                try:
                    x = int(tds[0].text.strip())
                    ch =   tds[1].text.strip() or ' '
                    y = int(tds[2].text.strip())
                    points.append((x, y, ch))
                    if x > max_x: max_x = x
                    if y > max_y: max_y = y
                except (ValueError, AttributeError):
                    pass
        # free the element to keep memory small
        tr.clear()

    # now build just as many rows as needed, on-the-fly
    from itertools import groupby
    points.sort(key=lambda p: p[1])  # sort by y
    for y, group in groupby(points, key=lambda p: p[1]):
        row_map = {x: ch for x, _, ch in group}
        # join each column in [0..max_x]
        print(''.join(row_map.get(x, ' ') for x in range(max_x+1)))



if __name__ == "__main__":
    # Example usage: replace with your published-doc URL
    url = "https://docs.google.com/document/d/e/2PACX-1vQGUck9HIFCyezsrBSnmENk5ieJuYwpt7YHYEzeNJkIb9OSDdx-ov2nRNReKQyey-cwJOoEKUhLmN9z/pub"
    decode_secret_message(url)
